@import "tailwindcss";

:root {
  --background: #FAFAFA;
  --foreground: #171717;
  --card: #FFFFFF;
  --card-foreground: #171717;
  --primary: #00c758;
  --primary-foreground: #FFFFFF;
  --secondary: #f3f4f6;
  --secondary-foreground: #374151;
  --muted: #f9fafb;
  --muted-foreground: #6b7280;
  --accent: #f3f4f6;
  --accent-foreground: #374151;
  --destructive: #ef4444;
  --destructive-foreground: #FFFFFF;
  --border: rgba(240, 240, 240, 1.0);
  --input: #e5e7eb;
  --ring: #00c758;
  --container-2x: 45rem;
}

.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
  --card: #1d1d1d;
  --card-foreground: #ededed;
  --primary: #00c758;
  --primary-foreground: #FFFFFF;
  --secondary: #262626;
  --secondary-foreground: #d4d4d8;
  --muted: #262626;
  --muted-foreground: #a1a1aa;
  --accent: #262626;
  --accent-foreground: #d4d4d8;
  --destructive: #ef4444;
  --destructive-foreground: #FFFFFF;
  --border: transparent;
  --input: #404040;
  --ring: #00c758;
  --container-2x: 45rem;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --font-sans: var(--font-inter);
  --container-2x: var(--container-2x);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), system-ui, -apple-system, sans-serif;
  transition: background-color 0.2s ease, color 0.2s ease;
}

/* Add smooth transitions for theme-aware elements */
.bg-card,
.bg-background,
.bg-muted,
.text-foreground,
.text-muted-foreground {
  transition: background-color 0.2s ease, color 0.2s ease, var(--border) 0.2s ease;
}

/* CLS Prevention - Ensure images maintain layout */
img {
  /* Prevent layout shifts during image loading */
  display: block;
  max-width: 100%;
  height: 100%;
}

/* Aspect ratio containers for consistent image sizing */
.aspect-square {
  aspect-ratio: 1 / 1;
}

.aspect-flag {
  aspect-ratio: 3 / 2;
}

/* Prevent layout shifts from font loading */
.font-display-swap {
  font-display: swap;
}

/* Skeleton loading improvements */
.skeleton {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
