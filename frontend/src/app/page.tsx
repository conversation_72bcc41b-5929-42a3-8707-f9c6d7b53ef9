'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Sidebar from '@/components/Sidebar';
import MainContent from '@/components/MainContent';
import RightSidebar from '@/components/RightSidebar';
import Footer from '@/components/Footer';

export default function Home() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false); // Start with false to prevent hydration mismatch
  const [isRightSidebarOpen, setIsRightSidebarOpen] = useState(false); // Right sidebar state
  const [selectedLeagueId, setSelectedLeagueId] = useState<number | null>(null);

  // Handle responsive sidebars
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setIsSidebarOpen(false);
        setIsRightSidebarOpen(false);
      } else if (window.innerWidth < 1200) {
        setIsSidebarOpen(true);
        setIsRightSidebarOpen(false);
      } else {
        setIsSidebarOpen(true);
        setIsRightSidebarOpen(true);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const toggleRightSidebar = () => {
    setIsRightSidebarOpen(!isRightSidebarOpen);
  };

  const handleLeagueSelect = (leagueId: number) => {
    setSelectedLeagueId(selectedLeagueId === leagueId ? null : leagueId);
  };

  const handleClearLeagueFilter = () => {
    setSelectedLeagueId(null);
  };

  // Note: Removed the page-level skeleton loading to prevent double skeleton loading
  // Individual components (MainContent, Sidebar) handle their own loading states

  return (
    <div className="min-h-screen bg-background">
      <Header onToggleSidebar={toggleSidebar} onToggleRightSidebar={toggleRightSidebar} />

      <div className="flex justify-center">
        <div className="flex max-w-screen-2xl w-full">
          {/* Left Sidebar */}
          <div className={`${isSidebarOpen ? 'block' : 'hidden'} md:block`}>
            <Sidebar
              onLeagueSelect={handleLeagueSelect}
              selectedLeagueId={selectedLeagueId}
            />
          </div>

          {/* Main Content */}
          <div className="flex-1 min-w-0">
            <MainContent
              selectedLeagueId={selectedLeagueId}
              onClearLeagueFilter={handleClearLeagueFilter}
            />
          </div>

          {/* Right Sidebar */}
          <div className={`${isRightSidebarOpen ? 'block' : 'hidden'} xl:block`}>
            <RightSidebar />
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
